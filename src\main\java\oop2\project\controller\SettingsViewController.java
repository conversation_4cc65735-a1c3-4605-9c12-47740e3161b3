package oop2.project.controller;

import javafx.fxml.FXML;
import javafx.scene.control.*;
import oop2.project.config.AppConfig;

public class SettingsViewController {
    @FXML
    private ComboBox<String> languageComboBox;

    @FXML
    private CheckBox allowAdultCheckBox;

    @FXML
    private Spinner<Double> minRatingSpinner;

    @FXML
    private TextField openAiKeyField;

    @FXML
    private TextField tmdbKeyField;

    @FXML
    private Button saveButton;

    private final AppConfig config = AppConfig.getInstance();

    @FXML
    public void initialize() {


        // Load config values into UI
        languageComboBox.setValue(config.get(AppConfig.KEY_LANGUAGE));
        allowAdultCheckBox.setSelected(config.get(AppConfig.KEY_ALLOW_ADULT_CONTENT, Boolean.class));
        minRatingSpinner.setValueFactory(new SpinnerValueFactory.DoubleSpinnerValueFactory(0.5, 10.0, config.get(AppConfig.KEY_MINIMUM_RATING, Double.class), 0.5));
        openAiKeyField.setText(config.get(AppConfig.KEY_OPENAI_API_KEY));
        tmdbKeyField.setText(config.get(AppConfig.KEY_TMDB_API_KEY));

        // Save config on button click
        saveButton.setOnAction(e -> saveSettings());
    }

    private void saveSettings() {
        config.set(AppConfig.KEY_LANGUAGE, languageComboBox.getValue());
        config.set(AppConfig.KEY_ALLOW_ADULT_CONTENT, Boolean.toString(allowAdultCheckBox.isSelected()));
        config.set(AppConfig.KEY_MINIMUM_RATING, minRatingSpinner.getValue().toString());
        config.set(AppConfig.KEY_OPENAI_API_KEY, openAiKeyField.getText());
        config.set(AppConfig.KEY_TMDB_API_KEY, tmdbKeyField.getText());

        config.save(); // You'll create this method below
    }
}
