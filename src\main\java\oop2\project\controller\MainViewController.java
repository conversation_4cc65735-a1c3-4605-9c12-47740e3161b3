package oop2.project.controller;

import javafx.fxml.*;
import javafx.scene.*;
import javafx.scene.control.Button;
import javafx.scene.input.MouseEvent;
import javafx.scene.layout.*;
import oop2.project.Main;

import java.io.IOException;

public class MainViewController {

    @FXML
    private StackPane contentArea;
    
    @FXML
    private Button movieSearcherNavButton;

    @FXML
    private Button settingsNavButton;

    @FXML
    private void onMovieSearcherButtonClicked(MouseEvent mouseEvent) {
        System.out.println( "Clicked");
    }

    @FXML
    private void onSettingsButtonClicked() {
        try {
            FXMLLoader loader = new FXMLLoader(Main.class.getResource("view/SettingsView.fxml"));
            Parent settings = loader.load();
            contentArea.getChildren().clear();
            contentArea.getChildren().add(settings);
        } catch (IOException e) {
            // TODO: Logging
            System.out.println( "Error loading settings view" + e.getLocalizedMessage());
        }
    }

    @FXML
    private void onExitButtonClicked(MouseEvent mouseEvent) {
        System.exit(0);
    }
}
