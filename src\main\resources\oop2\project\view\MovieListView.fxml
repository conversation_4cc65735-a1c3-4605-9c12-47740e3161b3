<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import org.kordamp.ikonli.javafx.FontIcon?>

<?scenebuilder-stylesheet ../css/MovieListView.css?>

<VBox xmlns="http://javafx.com/javafx/17.0.12"
      xmlns:fx="http://javafx.com/fxml/1"
      spacing="20.0"
      styleClass="movie-list-container">
    
    <padding>
        <Insets bottom="20.0" left="20.0" right="20.0" top="20.0"/>
    </padding>

    <!-- Header Section -->
    <VBox spacing="15.0" styleClass="header-section">
        <HBox alignment="CENTER_LEFT" spacing="15.0">
            <Label text="Movie Collection" styleClass="page-title"/>
            <Region HBox.hgrow="ALWAYS"/>
            <Label fx:id="movieCountLabel" text="0 movies" styleClass="movie-count"/>
        </HBox>
        
        <!-- Search and Filter Bar -->
        <HBox spacing="15.0" alignment="CENTER_LEFT" styleClass="search-filter-bar">
            <!-- Search Field -->
            <HBox spacing="8.0" alignment="CENTER_LEFT" styleClass="search-container" HBox.hgrow="ALWAYS">
                <FontIcon iconLiteral="fas-search" styleClass="search-icon"/>
                <TextField fx:id="searchField" 
                          promptText="Search movies by title, genre, or year..." 
                          styleClass="search-field"
                          HBox.hgrow="ALWAYS"/>
                <Button fx:id="clearSearchButton" styleClass="clear-search-btn">
                    <graphic>
                        <FontIcon iconLiteral="fas-times" iconSize="12"/>
                    </graphic>
                </Button>
            </HBox>
            
            <!-- Filter Controls -->
            <ComboBox fx:id="genreFilterCombo" 
                     promptText="All Genres" 
                     styleClass="filter-combo"
                     prefWidth="150.0"/>
            
            <ComboBox fx:id="yearFilterCombo" 
                     promptText="All Years" 
                     styleClass="filter-combo"
                     prefWidth="120.0"/>
            
            <ComboBox fx:id="sortCombo" 
                     promptText="Sort by..." 
                     styleClass="filter-combo"
                     prefWidth="140.0"/>
            
            <!-- View Toggle -->
            <ToggleGroup fx:id="viewToggleGroup"/>
            <ToggleButton fx:id="gridViewToggle" 
                         toggleGroup="$viewToggleGroup" 
                         selected="true"
                         styleClass="view-toggle">
                <graphic>
                    <FontIcon iconLiteral="fas-th" iconSize="14"/>
                </graphic>
            </ToggleButton>
            <ToggleButton fx:id="listViewToggle" 
                         toggleGroup="$viewToggleGroup"
                         styleClass="view-toggle">
                <graphic>
                    <FontIcon iconLiteral="fas-list" iconSize="14"/>
                </graphic>
            </ToggleButton>
        </HBox>
    </VBox>

    <!-- Main Content Area -->
    <StackPane VBox.vgrow="ALWAYS" styleClass="content-area">
        
        <!-- Movie Grid/List Container -->
        <ScrollPane fx:id="movieScrollPane" 
                   fitToWidth="true" 
                   styleClass="movie-scroll-pane"
                   hbarPolicy="NEVER">
            <content>
                <VBox spacing="20.0">
                    <!-- Grid View Container -->
                    <FlowPane fx:id="movieGridContainer" 
                             hgap="20.0" 
                             vgap="20.0" 
                             styleClass="movie-grid"
                             alignment="TOP_LEFT"/>
                    
                    <!-- List View Container (Initially Hidden) -->
                    <VBox fx:id="movieListContainer" 
                          spacing="10.0" 
                          styleClass="movie-list"
                          visible="false"
                          managed="false"/>
                </VBox>
            </content>
        </ScrollPane>
        
        <!-- Loading Indicator -->
        <VBox fx:id="loadingIndicator" 
              alignment="CENTER" 
              spacing="15.0" 
              styleClass="loading-container"
              visible="false">
            <ProgressIndicator styleClass="loading-spinner"/>
            <Label text="Loading movies..." styleClass="loading-text"/>
        </VBox>
        
        <!-- Empty State -->
        <VBox fx:id="emptyStateContainer" 
              alignment="CENTER" 
              spacing="20.0" 
              styleClass="empty-state"
              visible="false">
            <FontIcon iconLiteral="fas-film" iconSize="64" styleClass="empty-state-icon"/>
            <Label text="No movies found" styleClass="empty-state-title"/>
            <Label text="Try adjusting your search criteria or add some movies to your collection" 
                   styleClass="empty-state-subtitle"
                   wrapText="true"
                   textAlignment="CENTER"
                   maxWidth="400.0"/>
            <Button fx:id="addMovieButton" text="Add Movies" styleClass="add-movie-btn">
                <graphic>
                    <FontIcon iconLiteral="fas-plus" iconSize="14"/>
                </graphic>
            </Button>
        </VBox>
        
        <!-- Error State -->
        <VBox fx:id="errorStateContainer" 
              alignment="CENTER" 
              spacing="20.0" 
              styleClass="error-state"
              visible="false">
            <FontIcon iconLiteral="fas-exclamation-triangle" iconSize="64" styleClass="error-state-icon"/>
            <Label text="Something went wrong" styleClass="error-state-title"/>
            <Label fx:id="errorMessageLabel" 
                   text="Unable to load movies. Please try again." 
                   styleClass="error-state-subtitle"
                   wrapText="true"
                   textAlignment="CENTER"
                   maxWidth="400.0"/>
            <Button fx:id="retryButton" text="Try Again" styleClass="retry-btn">
                <graphic>
                    <FontIcon iconLiteral="fas-redo" iconSize="14"/>
                </graphic>
            </Button>
        </VBox>
    </StackPane>

    <!-- Pagination Section -->
    <HBox alignment="CENTER" spacing="15.0" styleClass="pagination-section">
        <Button fx:id="firstPageButton" styleClass="pagination-btn">
            <graphic>
                <FontIcon iconLiteral="fas-angle-double-left" iconSize="14"/>
            </graphic>
        </Button>
        <Button fx:id="prevPageButton" styleClass="pagination-btn">
            <graphic>
                <FontIcon iconLiteral="fas-angle-left" iconSize="14"/>
            </graphic>
        </Button>
        
        <HBox fx:id="pageNumberContainer" spacing="5.0" alignment="CENTER"/>
        
        <Button fx:id="nextPageButton" styleClass="pagination-btn">
            <graphic>
                <FontIcon iconLiteral="fas-angle-right" iconSize="14"/>
            </graphic>
        </Button>
        <Button fx:id="lastPageButton" styleClass="pagination-btn">
            <graphic>
                <FontIcon iconLiteral="fas-angle-double-right" iconSize="14"/>
            </graphic>
        </Button>
        
        <Region HBox.hgrow="ALWAYS"/>
        
        <Label text="Items per page:" styleClass="pagination-label"/>
        <ComboBox fx:id="itemsPerPageCombo" 
                 styleClass="pagination-combo"
                 prefWidth="80.0"/>
    </HBox>
</VBox>
